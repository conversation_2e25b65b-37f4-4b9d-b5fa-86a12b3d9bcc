package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	util "socks/client/util"
	"socks/relay"
	common "socks/relay/common"

	"github.com/xtaci/smux"
)

// SafeConn now wraps a TCP connection instead of WebSocket
type SafeConn struct {
	Conn    net.Conn      // 底层TCP连接
	session *smux.Session // smux会话，用于多路复用
}

func (c *SafeConn) Close() error {
	if c.session != nil {
		c.session.Close()
	}
	return c.Conn.Close()
}

// GetSession 获取smux会话
func (c *SafeConn) GetSession() *smux.Session {
	return c.session
}

// getNetworkType 根据协议类型获取网络类型
func getNetworkType(proto relay.ProtoType) string {
	switch proto {
	case relay.ProtoTCP, relay.ProtoHTTP:
		return "tcp"
	case relay.ProtoTCP4:
		return "tcp4"
	case relay.ProtoTCP6:
		return "tcp6"
	case relay.ProtoUDP:
		return "udp"
	case relay.ProtoUDP4:
		return "udp4"
	case relay.ProtoUDP6:
		return "udp6"
	case relay.ProtoUNIX:
		return "unix"
	case relay.ProtoUNIXGRAM:
		return "unixgram"
	case relay.ProtoUNIXPACK:
		return "unixpacket"
	default:
		return "tcp" // 默认使用tcp
	}
}

// handleStreamConnections 处理基于smux.Stream的连接
func handleStreamConnections(config *Config) {
	session := globalConn.GetSession()
	if session == nil {
		log.Printf("Failed to get smux session")
		return
	}

	for {
		// 循环接收新的stream
		stream, err := session.AcceptStream()
		if err != nil {
			log.Printf("Failed to accept stream: %v", err)
			// 如果session关闭，触发重连
			go reconnectUntilSuccess(config)
			break
		}

		// 为每个stream启动处理goroutine
		go handleSingleStream(stream, config)
	}
}

// handleSingleStream 处理单个stream连接
func handleSingleStream(stream net.Conn, config *Config) {
	defer stream.Close()

	// 1. 解码relay.Request协议头
	request, err := relay.DecodeRequestMessage(stream)
	if err != nil {
		log.Printf("Failed to decode relay request: %v", err)
		// 发送错误确认消息
		confirmMsg := relay.NewConfirmResponseMessage(false, fmt.Sprintf("decode request failed: %v", err))
		confirmBytes, _ := confirmMsg.Encode()
		stream.Write(confirmBytes)
		return
	}
	// 2. 建立与目标服务的连接
	localConn, err := net.Dial(getNetworkType(request.Proto), request.Address)
	if err != nil {
		log.Printf("Failed to connect to service: %s, %v", request.Address, err)
		confirmMsg := relay.NewConfirmResponseMessage(false, fmt.Sprintf("connect failed: %v", err))
		confirmBytes, _ := confirmMsg.Encode()
		stream.Write(confirmBytes)
		return
	}
	defer localConn.Close()

	// 3. 发送成功确认消息
	confirmMsg := relay.NewConfirmResponseMessage(true, "connection established")
	confirmBytes, err := confirmMsg.Encode()
	if err != nil {
		log.Printf("Failed to encode confirm message: %v", err)
		return
	}

	if _, err := stream.Write(confirmBytes); err != nil {
		log.Printf("Failed to send confirm message: %v", err)
		return
	}

	// 5. 根据协议类型进行不同的数据转发处理
	if request.Proto == relay.ProtoHTTP {
		// HTTP代理：需要进行HTTP解析
		// 使用goroutine转发请求数据
		go func() {
			defer stream.Close()
			io.Copy(localConn, stream)
		}()

		// 使用ReadResponse读取完整的http响应，从而达到关闭stream的目的
		br := bufio.NewReader(localConn)
		resp, err := http.ReadResponse(br, nil)
		if err != nil {
			log.Printf("read response error: %v", err)
			stream.Close()
			return
		}
		defer resp.Body.Close()

		if err := resp.Write(stream); err != nil {
			log.Printf("write response error: %v", err)
		}
	} else {
		// 端口代理：直接进行字节流转发
		go func() {
			defer stream.Close()
			io.Copy(stream, localConn)
		}()

		_, err = io.Copy(localConn, stream)
		if err != nil {
			log.Printf("Failed to copy data to local connection: %v", err)
		}
	}
}

// Config 配置信息
type Config struct {
	TunnelClientConfig
	UUID string
}

type UrlMapping struct {
	Host string
	Port int
}

// PortMapping updated to use TCP connection
type PortMapping struct {
	LocalPort  string    // ip:port
	RemotePort int       // Remote port
	Conn       *SafeConn // TCP connection
	Created    time.Time // Creation time
}

type URLRegisterRequest struct {
	ApiType      string `json:"api_type"`      // Agent, AI, or API
	ServiceGroup string `json:"service_group"` // 服务组别
	ServiceName  string `json:"service_name"`  // 服务名称
	ServicePort  string `json:"service_port"`  // 服务端口
	AppName      string `json:"app_name"`      // 应用名称
	ClientUUID   string `json:"client_id"`     // 客户端UUID
	BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
}

type URLRegisterResponse struct {
	Success bool   `json:"success"`
	URLPath string `json:"url_path"`
}

// 全局映射管理
var (
	// 存储所有活跃的端口映射
	portMappings    = make(map[string]*PortMapping) // 代理ip:代理端口 -> 映射信息
	portMappingsMux sync.Mutex

	urlMappings    = make(map[string]map[string]*UrlMapping) // [AppName][baseURL]*UrlMapping
	urlMappingsMux sync.Mutex
)

// 全局控制连接
var (
	globalConn *SafeConn

	globalPortMappings    = make(map[int]*PortMapping) // 服务端口 -> 映射信息
	globalPortMappingsMux sync.RWMutex
)

// parseFlags 解析命令行参数
func parseFlags() (*Config, error) {
	var configPath string
	config := &Config{
		UUID: util.GenerateUUID(),
	}

	flag.StringVar(&configPath, "config", "", "配置文件路径")
	flag.StringVar(&config.ServerIP, "server", config.ServerIP, "中转服务器IP")
	flag.StringVar(&config.LocalHost, "host", config.LocalHost, "本地服务IP")
	flag.StringVar(&config.Type, "type", config.Type, "代理客户端类型")
	flag.StringVar(&config.Group, "group", config.Group, "代理客户端组别")
	flag.StringVar(&config.HostName, "name", config.HostName, "代理客户端名称")
	flag.IntVar(&config.ServerPort, "port", config.ServerPort, "中转服务器端口")
	flag.IntVar(&config.APIPort, "manager", config.APIPort, "代理客户端API服务端口")
	flag.Parse()

	if config.ServerIP == "" || config.ServerPort == 0 {
		config.TunnelClientConfig = GetTunnelConfig(configPath)
	}

	return config, nil
}

// registerURLMapping 向服务器注册URL映射
func registerURLMapping(config *Config, baseURL, appName, serviceName, serviceGroup, apiType, ipPort string) (*URLRegisterResponse, error) {
	// 构建请求体

	if address := strings.Split(ipPort, ":"); len(address) == 1 {
		ipPort = "localhost:" + ipPort
	}
	requestBody := URLRegisterRequest{
		ApiType:      apiType,
		ServiceGroup: serviceGroup,
		ServiceName:  serviceName,
		ServicePort:  ipPort,
		AppName:      appName,
		ClientUUID:   config.UUID,
		BaseURL:      baseURL,
	}

	requestData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("json encode failed: %v", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("http://%s:%d/url/register",
		config.ServerIP, config.ServerPort)

	// 发送POST请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("register url mapping failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("register url mapping failed: %s", string(body))
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read server response body failed: %w", err)
	}
	// 解析响应体
	var response *URLRegisterResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		// JSON parse failed
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	log.Printf("URL mapping registered successfully: %s", response.URLPath)
	return response, nil
}

// unregisterURLMapping 向服务器取消注册URL映射
func unregisterURLMapping(config *Config, urlPath string) error {
	// Build request URL
	url := fmt.Sprintf("http://%s:%d/url/unregister?client_id=%s&url_path=%s",
		config.ServerIP, config.ServerPort, config.UUID, urlPath)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create unregister request: %v", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to unregister URL mapping: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to unregister URL mapping: %s", string(body))
	}

	log.Printf("URL mapping unregistered successfully: %s", urlPath)
	return nil
}

func register(config *Config) (*SafeConn, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("Failed to get local IP: %v, using default IP", err)
	}

	// 获取版本信息
	relayVersion := fmt.Sprintf("%d", relay.Version)

	// 从环境变量获取客户端版本
	clientVersion := "unknown"
	if envVersion, err := common.GetClientVersion(); err == nil {
		clientVersion = envVersion
	} else {
		log.Printf("Failed to get client version from environment: %v", err)
		// 尝试从标准环境变量获取
		if envVersion := os.Getenv("BOS_Client_Version"); envVersion != "" {
			clientVersion = envVersion
		}
	}

	// 构建请求URL
	serverAddr := fmt.Sprintf("%s:%d", config.ServerIP, config.ServerPort)
	url := fmt.Sprintf("http://%s/register?name=%s&type=%s&id=%s&ip=%s&group=%s&relay_version=%s&client_version=%s",
		serverAddr, config.HostName, config.Type, config.UUID, localIP, config.Group, relayVersion, clientVersion)

	log.Printf("Connecting to server: %s", url)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %v", err)
	}

	// 创建TCP连接
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("connect failed: %v", err)
	}

	// 发送HTTP请求
	err = req.Write(conn)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("send HTTP request failed: %v", err)
	}

	// 读取HTTP响应头
	resp, err := http.ReadResponse(bufio.NewReader(conn), req)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("read response failed: %v", err)
	}
	log.Printf("sever response: %v", resp)
	// 检查响应状态
	if resp.StatusCode != http.StatusSwitchingProtocols {
		body, _ := io.ReadAll(resp.Body)
		conn.Close()
		return nil, fmt.Errorf("unexpected status code: %s, response body: %s", resp.Status, string(body))
	}

	// 设置TCP连接参数
	tcpConn := conn.(*net.TCPConn)
	tcpConn.SetKeepAlive(true)
	tcpConn.SetKeepAlivePeriod(30 * time.Second)
	tcpConn.SetWriteBuffer(32 * 1024)
	tcpConn.SetReadBuffer(32 * 1024)

	// 创建smux会话（客户端模式）
	// 配置smux参数，优化并发性能和保活机制
	sconfig := smux.DefaultConfig()
	sconfig.MaxReceiveBuffer = 4194304           // 4MB 接收缓冲区
	sconfig.MaxStreamBuffer = 2097152            // 2MB 流缓冲区
	sconfig.KeepAliveInterval = 10 * time.Second // 更频繁的心跳检测
	sconfig.KeepAliveTimeout = 30 * time.Second  // 更短的超时时间以便快速检测连接问题
	sconfig.MaxFrameSize = 32768                 // 32KB 帧大小
	sconfig.KeepAliveDisabled = false            // 确保开启 keepalive
	session, err := smux.Client(conn, sconfig)
	if err != nil {
		log.Printf("Failed to create smux session: %v", err)
		conn.Close()
		return nil, fmt.Errorf("create smux session failed: %v", err)
	}

	// 创建安全连接，使用更大的缓冲区
	safeConn := &SafeConn{
		Conn:    conn,
		session: session,
	}

	log.Printf("successfully registered client and established control connection")

	return safeConn, nil
}

func buildTunnel(config *Config, ipPort string, serviceName string) (int, error) {

	// 构建请求URL，包含本机IP和服务名称
	url := fmt.Sprintf("http://%s:%d/allocate?id=%s&port=%s&service_name=%s",
		config.ServerIP, config.ServerPort, config.UUID, ipPort, serviceName)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("allocate port failed: %v", err)
	}
	defer resp.Body.Close()

	// 解析服务端返回的端口信息
	var result struct {
		Port int `json:"port"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, fmt.Errorf("parse response failed: %v", err)
	}

	log.Printf("successfully allocated port mapping: local port %s -> server %d",
		ipPort, result.Port)

	return result.Port, nil
}

// 重连函数 - 一次性调用，直至重连完成才返回
func reconnectUntilSuccess(config *Config) {
	log.Printf("connection lost, starting reconnect mechanism...")

	for {
		log.Printf("Trying to reconnect to server...")
		newConn, err := register(config)
		if err != nil {
			log.Printf("Reconnect failed: %v, will retry in 30 seconds", err)
			time.Sleep(30 * time.Second)
			continue
		}

		// 重连成功，更新全局连接
		log.Printf("Reconnect succeeded, updating global connection and connection manager")

		// 关闭旧连接（如果存在）
		if globalConn != nil {
			globalConn.Close()
		}

		// 更新全局连接
		globalConn = newConn

		// 重新启动连接管理
		buildGlobalCM(config)

		log.Printf("Reconnect complete, service is back to normal")
		return // 重连成功，退出函数
	}
}

// 获取或创建全局控制连接
func buildGlobalCM(config *Config) {
	// 启动基于smux.Stream的消息处理
	go handleStreamConnections(config)
}

// buildPortMapping 新的端口映射逻辑，复用全局控制连接和连接管理器
func buildPortMapping(config *Config, ipPort string, serviceName string) (*PortMapping, error) {
	// 检查全局连接是否可用
	if globalConn == nil {
		return nil, fmt.Errorf("global connection not built, please wait for reconnect to complete")
	}

	// Check if mapping already exists
	portMappingsMux.Lock()
	defer portMappingsMux.Unlock()
	if mapping, exists := portMappings[ipPort]; exists {
		return mapping, nil
	}

	if address := strings.Split(ipPort, ":"); len(address) == 1 {
		ipPort = "localhost:" + ipPort
	}

	// 申请远程端口
	remotePort, err := buildTunnel(config, ipPort, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to apply for remote port: %v", err)
	}
	log.Printf("Successfully applied for port mapping: service %s -> remote port %d", ipPort, remotePort)

	// 创建映射记录，使用全局连接管理器
	mapping := &PortMapping{
		LocalPort:  ipPort,
		RemotePort: remotePort,
		Created:    time.Now(),
	}

	// 保存映射
	globalPortMappingsMux.Lock()
	globalPortMappings[remotePort] = mapping
	globalPortMappingsMux.Unlock()

	return mapping, nil
}

// startAPIServer 启动API服务器
func startAPIServer(config *Config) {

	// 状态端点
	http.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type MappingInfo struct {
			LocalPort  string    `json:"local_port"` // ip:port
			RemotePort int       `json:"remote_port"`
			Created    time.Time `json:"created"`
		}

		var mappings []MappingInfo

		portMappingsMux.Lock()
		for _, mapping := range portMappings {
			mappings = append(mappings, MappingInfo{
				LocalPort:  mapping.LocalPort,
				RemotePort: mapping.RemotePort,
				Created:    mapping.Created,
			})
		}
		portMappingsMux.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"server":   fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort),
			"mappings": mappings,
			"count":    len(mappings),
		})
	})

	http.HandleFunc("/tunnel", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost && r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取本地端口
		ipPort := r.URL.Query().Get("ip_port")
		if ipPort == "" {
			http.Error(w, "Missing ip port parameter", http.StatusBadRequest)
			return
		}

		serviceName := r.URL.Query().Get("service_name")

		// 设置映射
		mapping, err := buildPortMapping(config, ipPort, serviceName)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回映射信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"local_port":  mapping.LocalPort,
			"remote_port": mapping.RemotePort,
			"server":      config.ServerIP,
			"created":     mapping.Created,
		})
	})

	// URL注册端点
	http.HandleFunc("/url/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type URLRegisterRequest struct {
			AppName      string `json:"app_name"`
			ServiceName  string `json:"service_name"`
			ServiceGroup string `json:"service_group"`
			IpPort       string `json:"service_address"`
			ApiType      string `json:"api_type"`
			BaseURL      string `json:"base_url"`
		}

		var req URLRegisterRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "illegal request body", http.StatusBadRequest)
			return
		}

		if req.AppName == "" {
			http.Error(w, "Missing name parameter", http.StatusBadRequest)
			return
		}

		if req.ServiceGroup == "" {
			http.Error(w, "Missing group parameter", http.StatusBadRequest)
			return
		}

		if req.ApiType == "" {
			http.Error(w, "Missing type parameter", http.StatusBadRequest)
			return
		}

		if req.IpPort == "" {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		if isBaseUrlRegistered(req.AppName, req.BaseURL) {
			http.Error(w, "url already registed", http.StatusBadRequest)
			return
		}

		// 向server注册URL映射
		response, err := registerURLMapping(config, req.BaseURL, req.AppName, req.ServiceName, req.ServiceGroup, util.GetFormatServiceType(req.ApiType), req.IpPort)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		addBaseUrlMapping(req.AppName, req.BaseURL, req.IpPort)

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// URL取消注册端点
	http.HandleFunc("/url/unregister", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodDelete {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取参数
		baseURL := r.URL.Query().Get("url")
		if baseURL == "" {
			http.Error(w, "Missing url_path parameter", http.StatusBadRequest)
			return
		}

		// 向server取消注册URL映射
		err := unregisterURLMapping(config, baseURL)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":  true,
			"url_path": baseURL,
		})
	})

	// 启动API服务器
	apiAddr := fmt.Sprintf("%s:%d", config.LocalHost, config.APIPort)
	log.Printf("API server started at %s", apiAddr)
	if err := http.ListenAndServe(apiAddr, nil); err != nil {
		log.Fatalf("API server start failed: %v", err)
	}
}

func isBaseUrlRegistered(appName, baseURL string) bool {
	urlMappingsMux.Lock()
	defer urlMappingsMux.Unlock()
	if baseUrlMapping, ok := urlMappings[appName]; ok {
		_, ok := baseUrlMapping[baseURL]
		return ok
	}
	return false
}

func addBaseUrlMapping(appName, baseURL, ipPort string) {
	urlMappingsMux.Lock()
	defer urlMappingsMux.Unlock()

	var host, port string
	addressInfo := strings.Split(ipPort, ":")
	if len(addressInfo) == 1 {
		host = "localhost"
		port = addressInfo[0]
	} else if len(addressInfo) == 2 {
		host = addressInfo[0]
		port = addressInfo[1]
	} else {
		log.Printf("Invalid ipPort format: %s", ipPort)
		return
	}

	portNumber, err := strconv.Atoi(port)
	if err != nil {
		log.Printf("Invalid port number: %s", port)
		return
	}
	if baseUrlMapping, ok := urlMappings[appName]; ok {
		baseUrlMapping[baseURL] = &UrlMapping{Host: host, Port: portNumber}
	} else {
		urlMappings[appName] = map[string]*UrlMapping{baseURL: {Host: host, Port: portNumber}}
	}
}

func main() {
	// 解析命令行参数
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	config, err := parseFlags()
	if err != nil {
		flag.Usage()
		os.Exit(1)
	}

	log.Printf("Registering client...")
	globalConn, err = register(config)
	if err != nil {
		log.Printf("Client registration error: %v", err)
		os.Exit(1)
	}
	log.Printf("Client registered successfully, building global connection manager...")

	buildGlobalCM(config)
	log.Printf("Global connection manager built, starting API server...")

	// 加载配置文件中的隧道配置
	for _, tunnel := range config.Tunnels {
		switch tunnel.APIType {
		case "TCP", "UDP":
			_, err := buildPortMapping(config, tunnel.ServicePort, tunnel.ServiceName)
			if err != nil {
				log.Printf("Failed to build port mapping for tunnel %v: %v", tunnel, err)
			}
		default:
			_, err := registerURLMapping(config, tunnel.BaseURL, tunnel.AppName, tunnel.ServiceName, tunnel.ServiceGroup, tunnel.APIType, tunnel.ServicePort)
			if err != nil {
				log.Printf("Failed to register URL mapping for tunnel %v: %v", tunnel, err)
				continue
			}
			addBaseUrlMapping(tunnel.AppName, tunnel.BaseURL, tunnel.ServicePort)

		}
	}

	// 启动API服务器
	startAPIServer(config)

}
