package repo

import "time"

const (
	IntranetTunnelTableName = "gateway_intranet_tunnel"
)

type IntranetTunnel struct {
	ID          int       `gorm:"primaryKey;autoIncrement:true;column:id"`
	Name        string    `gorm:"not null;column:name"`
	ClientUUID  string    `gorm:"column:clientid"`
	ClientName  string    `gorm:"column:clientname"`
	ClientIp    string    `gorm:"column:clientip"`
	Protocol    string    `gorm:"column:protocol"`
	ServerPort  int       `gorm:"column:serverport"`
	ClientPort  string    `gorm:"column:clientport"`
	Enable      bool      `gorm:"column:enable"`
	Description string    `gorm:"column:description"`
	Encryption  bool      `gorm:"column:encryption"`
	Password    string    `gorm:"column:password"`
	RateLimit   int       `gorm:"column:ratelimit"`
	CreateTime  time.Time `gorm:"column:createtime"`
	Connected   time.Time `gorm:"column:lastconnectiontime"`
	Online      bool      `gorm:"column:online"`
	ClientType  string    `gorm:"column:clienttype"`
	ClientGroup string    `gorm:"column:clientgroup"`
	ServiceName string    `gorm:"column:servicename"`
	ClientRoute string    `gorm:"column:clientroute"` // 客户端的路由信息，用于URL代理
	ServerRoute string    `gorm:"column:serverroute"` // 服务端的路由信息，用于URL代理
}

// TableName 指定表名
func (IntranetTunnel) TableName() string {
	return IntranetTunnelTableName
}
