package common

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"unsafe"
)

const (
	// Linux环境变量文件路径
	LinuxEnvFilePath = "/BaseOS/.env"
)

// SetEnvironmentVariable 设置环境变量
// Windows: 直接设置系统环境变量
// Linux: 将环境变量写入 /BaseOS/.env 文件
func SetEnvironmentVariable(key, value string) error {
	if runtime.GOOS == "windows" {
		return setWindowsSystemEnv(key, value)
	} else {
		return setLinuxEnvFile(key, value)
	}
}

// GetEnvironmentVariable 获取环境变量
// Windows: 从系统环境变量获取
// Linux: 从 /BaseOS/.env 文件获取
func GetEnvironmentVariable(key string) (string, error) {
	if runtime.GOOS == "windows" {
		return getWindowsSystemEnv(key)
	} else {
		return getLinuxEnvFile(key)
	}
}

// setWindowsSystemEnv 设置Windows系统环境变量
func setWindowsSystemEnv(key, value string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("this function is only supported on Windows")
	}

	// 使用Windows API设置系统环境变量
	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	setEnvVar := kernel32.NewProc("SetEnvironmentVariableW")

	keyPtr, _ := syscall.UTF16PtrFromString(key)
	valuePtr, _ := syscall.UTF16PtrFromString(value)

	ret, _, err := setEnvVar.Call(
		uintptr(unsafe.Pointer(keyPtr)),
		uintptr(unsafe.Pointer(valuePtr)),
	)

	if ret == 0 {
		return fmt.Errorf("failed to set Windows environment variable: %v", err)
	}

	// 同时设置当前进程的环境变量
	return os.Setenv(key, value)
}

// getWindowsSystemEnv 获取Windows系统环境变量
func getWindowsSystemEnv(key string) (string, error) {
	if runtime.GOOS != "windows" {
		return "", fmt.Errorf("this function is only supported on Windows")
	}

	// 首先尝试从当前进程环境变量获取
	if value := os.Getenv(key); value != "" {
		return value, nil
	}

	// 如果当前进程没有，尝试从系统环境变量获取
	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	getEnvVar := kernel32.NewProc("GetEnvironmentVariableW")

	keyPtr, _ := syscall.UTF16PtrFromString(key)
	buffer := make([]uint16, 32767) // Windows环境变量最大长度

	ret, _, _ := getEnvVar.Call(
		uintptr(unsafe.Pointer(keyPtr)),
		uintptr(unsafe.Pointer(&buffer[0])),
		uintptr(len(buffer)),
	)

	if ret == 0 {
		return "", fmt.Errorf("environment variable not found: %s", key)
	}

	if ret > uintptr(len(buffer)) {
		return "", fmt.Errorf("environment variable value too long")
	}

	return syscall.UTF16ToString(buffer[:ret]), nil
}

// setLinuxEnvFile 在Linux系统中将环境变量写入文件
func setLinuxEnvFile(key, value string) error {
	// 确保目录存在
	dir := filepath.Dir(LinuxEnvFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %v", dir, err)
	}

	// 读取现有的环境变量
	envVars, err := readEnvFile()
	if err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to read existing env file: %v", err)
	}

	// 更新或添加环境变量
	envVars[key] = value

	// 写入文件
	return writeEnvFile(envVars)
}

// getLinuxEnvFile 从Linux环境变量文件获取值
func getLinuxEnvFile(key string) (string, error) {
	// 首先尝试从当前进程环境变量获取
	if value := os.Getenv(key); value != "" {
		return value, nil
	}

	// 从文件读取
	envVars, err := readEnvFile()
	if err != nil {
		if os.IsNotExist(err) {
			return "", fmt.Errorf("environment variable not found: %s", key)
		}
		return "", fmt.Errorf("failed to read env file: %v", err)
	}

	value, exists := envVars[key]
	if !exists {
		return "", fmt.Errorf("environment variable not found: %s", key)
	}

	return value, nil
}

// readEnvFile 读取环境变量文件
func readEnvFile() (map[string]string, error) {
	envVars := make(map[string]string)

	file, err := os.Open(LinuxEnvFilePath)
	if err != nil {
		return envVars, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析 KEY=VALUE 格式
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			// 移除值两端的引号（如果有）
			if len(value) >= 2 {
				if (strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"")) ||
					(strings.HasPrefix(value, "'") && strings.HasSuffix(value, "'")) {
					value = value[1 : len(value)-1]
				}
			}

			envVars[key] = value
		}
	}

	return envVars, scanner.Err()
}

// writeEnvFile 写入环境变量文件
func writeEnvFile(envVars map[string]string) error {
	file, err := os.Create(LinuxEnvFilePath)
	if err != nil {
		return fmt.Errorf("failed to create env file: %v", err)
	}
	defer file.Close()

	// 写入文件头注释
	if _, err := file.WriteString("# BaseOS Environment Variables\n"); err != nil {
		return err
	}
	if _, err := file.WriteString("# This file is automatically managed by the system\n\n"); err != nil {
		return err
	}

	// 写入环境变量
	for key, value := range envVars {
		// 如果值包含空格或特殊字符，用引号包围
		if strings.ContainsAny(value, " \t\n\r\"'\\") {
			value = fmt.Sprintf("\"%s\"", strings.ReplaceAll(value, "\"", "\\\""))
		}

		line := fmt.Sprintf("%s=%s\n", key, value)
		if _, err := file.WriteString(line); err != nil {
			return err
		}
	}

	return nil
}
