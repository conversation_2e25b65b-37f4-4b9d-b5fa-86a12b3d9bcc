package service

import (
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/monitor"
	"socks/server/util"
)

var (
	proxyService *ProxyService
	psOnce       sync.Once
)

type ProxyService struct {
	connection *event.Connection
	tunnels    map[int]*event.PortProxyTunnel
	sync.RWMutex
}

func GetProxyService(config *util.TunnelConfig) *ProxyService {
	psOnce.Do(func() {
		proxyService = &ProxyService{
			connection: event.GetConnection(config),
			tunnels:    make(map[int]*event.PortProxyTunnel),
		}
	})
	return proxyService
}

// 底层通讯层面的代理通讯
func (p *ProxyService) RegisterClient(client *entity.Client, conn net.Conn) error {
	err := p.connection.RegisterClient(client)
	if err != nil {
		return err
	}
	// 增加通道
	p.connection.AddTunnel(client.UUID, conn)
	// 激活通道，可以增加一个 ch 用来关闭通道
	go p.connection.ActivateTunnel(client.UUID)

	p.connection.UpdateClientPortOnlineStatus(client.UUID, true)
	p.connection.UpdateClientUrlOnlineStatus(client.UUID, true)
	return nil
}

// 端口层面的代理通讯-锁定一个端口，更新映射表
func (p *ProxyService) AllocateAvailablePort(clientUUID string, ipPort string, serviceName string) (int, error) {

	// 先查看映射表中是否存在映射
	mapping := p.connection.GetMapping(clientUUID, ipPort)
	if mapping != nil {
		return mapping.ServerPort, nil
	}

	// 检查客户端是否注册
	client := p.connection.GetClient(clientUUID)
	if client == nil {
		return 0, fmt.Errorf("client not register")
	}

	serverPort, err := p.connection.AllocatePort()
	if err != nil {
		return serverPort, err
	}

	now := time.Now()
	p.connection.AddMapping(clientUUID, ipPort, &entity.PortMapping{
		Name: client.GetTunnelName(ipPort),
		Client: &entity.Client{
			Name:  client.Name,
			IP:    client.IP,
			UUID:  client.UUID,
			Type:  "TCP",
			Group: client.Group,
		},
		ClientIpPort: ipPort,
		ServerPort:   serverPort,
		Listener:     p.connection.GetListener(serverPort),
		Created:      now,
		Connected:    now,
		Enable:       true,
		Online:       true,
		Encryption:   false,
		Password:     "",
		RateLimit:    0,
		ServiceName:  serviceName,
		Description:  "",
		Protocol: &entity.Protocol{
			TCP: true,
			UDP: false,
		},
	})

	// 初始化TCP流量监控
	trafficMonitor := monitor.GetGlobalMonitor()
	clientInfo := monitor.ClientInfo{
		UUID:  client.UUID,
		IP:    client.IP,
		Name:  client.Name,
		Group: client.Group,
	}

	err = trafficMonitor.CreateTCPMonitor(clientInfo, serverPort, serviceName)
	if err != nil {
		// 记录错误但不影响端口分配
		log.Printf("Failed to create TCP monitor for port %d: %v", serverPort, err)
	}

	return serverPort, nil
}

// 端口层面的代理通讯，将访问serverPort的请求通过tunnel转发至目标服务
// request -> server port -> listener(proxy) -> tunnel(chan-server) -> tunnel -> client port -> listener(client) ↓
// request <- server port <- listener(proxy) <- tunnel(chan-server) <- tunnel <- tunnel(chan-client) <------------
func (p *ProxyService) StartProxy(clientUUID string, ipPort string, serverPort int) error {
	p.Lock()
	defer p.Unlock()
	if _, ok := p.tunnels[serverPort]; ok {
		return fmt.Errorf("already proxy in server port: %d", serverPort)
	}

	// 创建tunnel退出时的清理回调函数
	onExit := func(port int) {
		p.Lock()
		defer p.Unlock()
		delete(p.tunnels, port)
		log.Printf("tunnel for server port %d has been cleaned up from tunnels map", port)
	}

	tunnel := event.NewPortProxyTunnel(p.connection, serverPort, ipPort, clientUUID, onExit)
	p.tunnels[serverPort] = tunnel
	go tunnel.StartProxy()
	return nil
}

func (p *ProxyService) StopProxy(serverPort int) error {
	p.Lock()
	defer p.Unlock()
	tunnel, ok := p.tunnels[serverPort]
	if !ok {
		log.Printf("proxy already removed, server port: %d", serverPort)
		return nil
	}
	delete(p.tunnels, serverPort)
	return tunnel.StopProxy()
}

func (p *ProxyService) RecoverFromDB() error {
	return p.connection.RecoverFromDB()
}

func (p *ProxyService) UpdateTunnelConfig(ids []int, all bool) error {
	return p.connection.UpdateFromDB(ids, all)
}

func (p *ProxyService) FilterClients(name, ip, uuid, clientType, group string) []*entity.Client {
	filter := event.ClientFilter{
		Name:  name,
		IP:    ip,
		UUID:  uuid,
		Type:  clientType,
		Group: group,
	}
	return p.connection.FilterClients(filter)
}

func (p *ProxyService) GetClientsByGroup() map[string][]*entity.Client {
	return p.connection.GetClientsByGroup()
}
