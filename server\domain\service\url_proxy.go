package service

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/monitor"
	"socks/server/util"
)

var (
	urlProxyService *URLProxyService
	upsOnce         sync.Once
)

type URLProxyService struct {
	connection *event.Connection
	tunnels    map[string]*event.URLProxyTunnel
	sync.RWMutex
}

func NewURLProxyService(config *util.TunnelConfig) *URLProxyService {
	return &URLProxyService{
		connection: event.GetConnection(config),
		tunnels:    make(map[string]*event.URLProxyTunnel),
	}
}

func GetUrlProxyService(config *util.TunnelConfig) *URLProxyService {
	upsOnce.Do(func() {
		urlProxyService = NewURLProxyService(config)
	})
	return urlProxyService
}

func (s *URLProxyService) RegisterClient(client *entity.Client) error {
	return s.connection.RegisterClient(client)
}

// RegisterURLMapping 注册URL映射
func (s *URLProxyService) RegisterURLMapping(clientUUID, appName, baseURL, serviceName, serviceGroup, apiType, ipPort string) (string, error) {
	// 检查客户端是否存在
	client := s.connection.GetClient(clientUUID)
	if client == nil {
		return "", fmt.Errorf("client not found: %s", clientUUID)
	}

	var urlPath string

	switch strings.ToUpper(apiType) {
	case "AGENT":
		urlPath = fmt.Sprintf("/Tunnel/Agent/%s/%s", client.Group, client.Name)
	case "AI":
		urlPath = fmt.Sprintf("/Tunnel/AI/%s/%s/%s", client.Group, appName, client.Name)
	case "ALG":
		urlPath = fmt.Sprintf("/Tunnel/ALG/%s/%s/%s", client.Group, appName, client.Name)
	case "API":
		urlPath = fmt.Sprintf("/Tunnel/API/%s/%s/%s", client.Group, appName, client.Name)
	default:
		return "", fmt.Errorf("invalid client_type: %s", apiType)
	}

	// 检查URL路径是否已被注册
	existingMapping := s.connection.GetURLMapping(urlPath)
	if existingMapping != nil {
		// 如果存在，且属于同一个客户端，更新映射
		// 同名不同uuid客户端暂不考虑
		if existingMapping.Client.UUID != clientUUID {
			return "", fmt.Errorf("url path already registered by another client: %s", urlPath)
		}
		existingMapping.BaseURL[baseURL] = &entity.ServiceInfo{
			ServiceName:  serviceName,
			ServiceGroup: serviceGroup,
			ServicePort:  ipPort,
			ServiceUrl:   baseURL,
		}
		s.connection.UpdateUrlMapping(existingMapping)
		return urlPath, nil
	}

	// 创建URL映射
	mapping := &entity.URLMapping{
		Name: appName,
		Client: &entity.Client{
			Name:  client.Name,
			IP:    client.IP,
			UUID:  client.UUID,
			Type:  apiType,
			Group: client.Group,
		},
		URLPath:     urlPath,
		BaseURL:     make(map[string]*entity.ServiceInfo, 0),
		Created:     time.Now(),
		Enable:      true,
		Online:      true,
		Description: fmt.Sprintf("URL mappings for %s", appName),
		Protocol:    entity.BuildProtocol(apiType),
	}
	mapping.BaseURL[baseURL] = &entity.ServiceInfo{
		ServiceName:  serviceName,
		ServiceGroup: serviceGroup,
		ServicePort:  ipPort,
		ServiceUrl:   baseURL,
	}

	// 添加映射
	s.connection.AddURLMapping(mapping)

	// 初始化HTTP流量监控
	trafficMonitor := monitor.GetGlobalMonitor()
	clientInfo := monitor.ClientInfo{
		UUID:  client.UUID,
		IP:    client.IP,
		Name:  client.Name,
		Group: client.Group,
	}

	// 为每个BaseURL创建HTTP监控记录
	for baseURL, serviceInfo := range mapping.BaseURL {
		err := trafficMonitor.CreateHTTPMonitor(clientInfo, baseURL, serviceInfo.ServiceName)
		if err != nil {
			// 记录错误但不影响映射创建
			fmt.Printf("Failed to create HTTP monitor for %s: %v\n", baseURL, err)
		}
	}

	return urlPath, nil
}

// UnregisterURLMapping 取消注册URL映射
func (s *URLProxyService) UnregisterURLMapping(clientUUID, urlPath, baseURL string) error {
	// 检查映射是否存在
	mapping := s.connection.GetURLMapping(urlPath)
	if mapping == nil {
		return fmt.Errorf("url mapping not found: %s", urlPath)
	}

	// 检查是否属于该客户端
	if mapping.Client.UUID != clientUUID {
		return fmt.Errorf("url mapping does not belong to client: %s", clientUUID)
	}

	// 删除映射的base_url
	s.connection.DeleteURLMappingByBaseUrl(urlPath, baseURL)

	return nil
}

// GetURLMapping 获取URL映射
func (s *URLProxyService) GetURLMapping(urlPath string) *entity.URLMapping {
	return s.connection.GetURLMapping(urlPath)
}

// GetClientURLMappings 获取客户端的所有URL映射
func (s *URLProxyService) GetClientURLMappings(clientUUID string) []string {
	return s.connection.GetClientURLMappings(clientUUID)
}

// UpdateOnlineStatus 更新在线状态
func (s *URLProxyService) UpdateOnlineStatus(clientUUID string, online bool) error {
	s.connection.UpdateClientUrlOnlineStatus(clientUUID, online)
	return nil
}

func (s *URLProxyService) AddTunnel(clientUUID string) *event.URLProxyTunnel {
	s.Lock()
	defer s.Unlock()
	s.tunnels[clientUUID] = event.NewURLProxyTunnel(s.connection, clientUUID)
	return s.tunnels[clientUUID]
}

func (s *URLProxyService) GetTunnel(clientUUID string) *entity.SafeConn {
	s.RLock()
	defer s.RUnlock()
	tunnel, ok := s.tunnels[clientUUID]
	if !ok {
		// 如果tunnel不存在，尝试从connection中获取SafeConn
		return s.connection.GetTunnel(clientUUID)
	}
	return tunnel.GetSafeConn()
}

func (s *URLProxyService) GetURLProxyTunnel(clientUUID string) *event.URLProxyTunnel {
	s.RLock()
	defer s.RUnlock()
	return s.tunnels[clientUUID]
}

func (s *URLProxyService) DeleteTunnel(clientUUID string) {
	s.Lock()
	defer s.Unlock()
	delete(s.tunnels, clientUUID)
}
