package dto

import "socks/server/domain/entity"

func <PERSON>Client(clientId, clientIp, clientName, clientType, clientGroup string) *entity.Client {
	return &entity.Client{
		UUID:  clientId,
		IP:    clientIp,
		Name:  clientName,
		Type:  clientType,
		Group: clientGroup,
	}
}

func NewClientWithVersions(clientId, clientIp, clientName, clientType, clientGroup, relayVersion, clientVersion string) *entity.Client {
	return &entity.Client{
		UUID:          clientId,
		IP:            clientIp,
		Name:          clientName,
		Type:          clientType,
		Group:         clientGroup,
		RelayVersion:  relayVersion,
		ClientVersion: clientVersion,
	}
}
