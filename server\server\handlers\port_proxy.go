package handlers

import (
	"encoding/json"
	"log"
	"net"
	"net/http"
	"socks/server/application/service"
	"socks/server/server/dto"
	"socks/server/util"
	"sync"
)

var (
	portProxyHandler *PortProxyHandler
	pphOnce          sync.Once
)

type PortProxyHandler struct {
	portProxyService service.PortProxyService
}

func GetPortProxyHandler() *PortProxyHandler {
	pphOnce.Do(func() {
		portProxyHandler = &PortProxyHandler{
			portProxyService: service.GetPortProxyService(util.SystemConfig),
		}
		portProxyHandler.init()
	})
	return portProxyHandler
}

// 分配一个公网端口，并为该 pc 启动监听
func (p *PortProxyHandler) AllocateHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取参数
	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}
	ipPort := r.URL.Query().Get("port")
	if ipPort == "" {
		http.Error(w, "missing port", http.StatusBadRequest)
		return
	}
	serviceName := r.URL.Query().Get("service_name")

	servicePort, err := p.portProxyService.AllocateServerPort(clientUUID, ipPort, serviceName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	p.portProxyService.Start(clientUUID, ipPort, servicePort)

	log.Printf("allocation server port:%d to client port:%s (client uuid:%s)", servicePort, ipPort, clientUUID)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"port": servicePort,
	})
}

// connectionHandler 处理控制连接请求
func (p *PortProxyHandler) RegisterHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求，后续会升级为TCP连接
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	clientName := r.URL.Query().Get("name")
	clientType := r.URL.Query().Get("type")
	clientGroup := r.URL.Query().Get("group")
	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}
	clientIP := r.URL.Query().Get("ip")
	if clientIP == "" {
		// 如果客户端没有上报IP，使用备用方法
		clientIP = r.RemoteAddr
		// 如果有代理，尝试获取真实IP
		if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			clientIP = forwardedFor
		}
		// 去除端口部分
		if host, _, err := net.SplitHostPort(clientIP); err == nil {
			clientIP = host
		}
	}

	client := dto.NewClient(clientUUID, clientIP, clientName, clientType, clientGroup)

	// 升级HTTP连接为TCP连接
	hj, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	conn, bufrw, err := hj.Hijack()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 发送HTTP 101状态码表示切换协议
	response := "HTTP/1.1 101 Switching Protocols\r\n" +
		"Upgrade: tcp\r\n" +
		"Connection: Upgrade\r\n\r\n"

	if _, err := bufrw.WriteString(response); err != nil {
		conn.Close()
		log.Printf("write upgrade response fail: %v", err)
		return
	}

	if err := bufrw.Flush(); err != nil {
		conn.Close()
		log.Printf("flush buffer fail: %v", err)
		return
	}

	log.Printf("connection upgrade to tcp, client id: %s", clientUUID)

	err = p.portProxyService.RegisterClient(client, conn)
	if err != nil {
		log.Print(err)
		return
	}
}

func (p *PortProxyHandler) RefreshHandler(w http.ResponseWriter, r *http.Request) {

	var reqBody struct {
		TunnelIDs  []int `json:"tunnel_ids"`
		RefreshAll bool  `json:"refresh_all"`
	}
	if err := json.NewDecoder(r.Body).Decode(&reqBody); err != nil {
		http.Error(w, "invalid request body: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 刷新端口代理配置
	err := p.portProxyService.UpdateTunnelConfig(reqBody.TunnelIDs, reqBody.RefreshAll)
	if err != nil {
		http.Error(w, "refresh tunnel config fail", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
	})
}

// FilterClientsHandler 处理客户端筛选请求
func (p *PortProxyHandler) FilterClientsHandler(w http.ResponseWriter, r *http.Request) {
	// 支持 GET 和 POST 请求
	if r.Method != http.MethodGet && r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var name, ip, uuid, clientType, group string

	if r.Method == http.MethodGet {
		// GET 请求从查询参数获取筛选条件
		name = r.URL.Query().Get("name")
		ip = r.URL.Query().Get("ip")
		uuid = r.URL.Query().Get("uuid")
		clientType = r.URL.Query().Get("type")
		group = r.URL.Query().Get("group")
	} else {
		// POST 请求从请求体获取筛选条件
		var req dto.ClientFilterRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "invalid request body: "+err.Error(), http.StatusBadRequest)
			return
		}
		name = req.Name
		ip = req.IP
		uuid = req.UUID
		clientType = req.Type
		group = req.Group
	}

	// 调用服务层进行筛选
	clients := p.portProxyService.FilterClients(name, ip, uuid, clientType, group)

	// 构建响应
	response := dto.ClientFilterResponse{
		Success: true,
		Data:    dto.ToClientInfoList(clients),
		Total:   len(clients),
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "encode response fail", http.StatusInternalServerError)
		return
	}
}

// GetClientsByGroupHandler 处理按组分组获取客户端的请求
func (p *PortProxyHandler) GetClientsByGroupHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受 GET 请求
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 调用服务层获取按组分组的客户端
	clientsByGroup := p.portProxyService.GetClientsByGroup()

	// 构建响应
	response := dto.ToClientGroupResponse(clientsByGroup)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "encode response fail", http.StatusInternalServerError)
		return
	}
}

// 进行一系列初始化活动
func (p *PortProxyHandler) init() error {
	err := p.portProxyService.RecoverFromDB()
	if err != nil {
		return err
	}

	return nil
}
