package service

import (
	"context"
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"syscall"
	"time"

	"socks/server/monitor"
	"socks/server/server/handlers"
	util "socks/server/util"
)

var (
	sysConfig           *util.TunnelConfig
	portCacheExpiration = 30 * time.Minute
	httpServer          *http.Server
)

// setupLogging 设置日志文件
func setupLogging() error {
	// 获取可执行文件路径
	exePath, err := os.Executable()
	if err != nil {
		return err
	}
	exeDir := filepath.Dir(exePath)
	logDir := filepath.Join(exeDir, "logs")

	// 创建logs目录
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return err
	}

	// 创建日志文件
	logFile := filepath.Join(logDir, "TunnelGateway.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}
	log.SetOutput(file)
	log.Println("日志系统初始化成功")
	return nil
}

// StartMainService 启动主服务逻辑
func StartMainService(ctx context.Context, args []string) error {
	return startMainServiceInternal(ctx, args, false)
}

// StartMainServiceForWindowsService 为Windows服务启动主服务逻辑
func StartMainServiceForWindowsService(ctx context.Context, args []string) error {
	return startMainServiceInternal(ctx, args, true)
}

// startMainServiceInternal 内部启动主服务逻辑
func startMainServiceInternal(ctx context.Context, args []string, isWindowsService bool) error {
	// 设置日志文件
	if err := setupLogging(); err != nil {
		log.Printf("Failed to setup logging: %v", err)
	}

	// 设置详细日志
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	// 解析命令行参数
	var configPath string
	var cacheExpiration int

	// 解析服务参数，过滤掉服务相关的参数
	var serviceArgs []string
	for i, arg := range args {
		if i == 0 {
			continue // 跳过程序名
		}
		// 跳过服务相关的参数
		if arg == "-install" || arg == "-uninstall" ||
			arg == "-start" || arg == "-stop" || arg == "-debug" {
			continue
		}
		serviceArgs = append(serviceArgs, arg)
	}

	// 只有当有参数时才解析
	if len(serviceArgs) > 0 {
		fs := flag.NewFlagSet("service", flag.ContinueOnError)
		configPathPtr := fs.String("config", "", "配置文件")
		cacheExpirationPtr := fs.Int("cache", 0, "端口缓存过期时间(分钟)")

		err := fs.Parse(serviceArgs)
		if err != nil {
			log.Printf("Failed to parse service arguments: %v", err)
			// 使用默认值继续运行，不退出
		} else {
			configPath = *configPathPtr
			cacheExpiration = *cacheExpirationPtr
		}
	}

	sysConfig = util.GetSystemConfig(configPath)

	// 使用配置的过期时间（天转换为分钟）
	defaultCacheExpiration := sysConfig.SlidingExpiration * 24 * 60 // 天转换为分钟

	// 如果没有通过命令行指定缓存时间，使用默认值
	if cacheExpiration == 0 {
		cacheExpiration = defaultCacheExpiration
	}

	// 设置缓存过期时间
	if cacheExpiration > 0 {
		portCacheExpiration = time.Duration(cacheExpiration) * time.Minute
	}

	// Start traffic monitoring system
	log.Printf("Starting traffic monitoring system...")
	monitor.StartMonitoring()
	log.Printf("Traffic monitoring system started successfully")

	// Start TCP proxy server
	tcpProxyHandler := handlers.GetTCPProxyHandler()
	if err := tcpProxyHandler.StartTCPProxyServer(sysConfig.URLProxyPort); err != nil {
		log.Printf("start TCP proxy server fail: %v", err)
		return err
	}

	// Start HTTP server for API
	log.Printf("HTTP API server listening on port %d", sysConfig.ManagerPort)
	log.Printf("TCP Proxy server listening on port %d", sysConfig.URLProxyPort)
	log.Printf("Server configuration: Port range %d-%d, Max connections %d, Timeout %ds",
		sysConfig.MinPort, sysConfig.MaxPort, sysConfig.MaxConnection, sysConfig.Timeout)
	log.Printf("Port cache expiration: %v", portCacheExpiration)

	// 设置HTTP路由
	mux := http.NewServeMux()
	mux.HandleFunc("/register", handlers.GetPortProxyHandler().RegisterHandler)
	mux.HandleFunc("/allocate", handlers.GetPortProxyHandler().AllocateHandler)
	mux.HandleFunc("/tunnel/refresh", handlers.GetPortProxyHandler().RefreshHandler)
	mux.HandleFunc("/clients/filter", handlers.GetPortProxyHandler().FilterClientsHandler)
	mux.HandleFunc("/clients/groups", handlers.GetPortProxyHandler().GetClientsByGroupHandler)

	mux.HandleFunc("/url/register", handlers.GetURLProxyHandler().RegisterURLHandler)
	mux.HandleFunc("/url/unregister", handlers.GetURLProxyHandler().UnregisterURLHandler)

	// 创建HTTP服务器
	httpServer = &http.Server{
		Addr:    "0.0.0.0:" + strconv.Itoa(sysConfig.ManagerPort),
		Handler: RequestLogger(mux),
	}

	// 启动HTTP服务器
	go func() {
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("HTTP server error: %v", err)
		}
	}()

	// 等待上下文取消或系统信号（Windows服务模式和控制台模式不同）
	if isWindowsService {
		// Windows服务模式：只等待上下文取消
		<-ctx.Done()
		log.Println("Service context cancelled, shutting down...")
	} else {
		// 控制台模式：等待上下文取消或系统信号
		select {
		case <-ctx.Done():
			log.Println("Service context cancelled, shutting down...")
		case <-waitForSignal():
			log.Println("Received shutdown signal, shutting down...")
		}
	}

	// 优雅关闭
	return shutdown()
}

// waitForSignal 等待系统信号
func waitForSignal() <-chan os.Signal {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	return sigChan
}

// shutdown 优雅关闭服务
func shutdown() error {
	log.Println("Shutting down service...")

	// 关闭HTTP服务器
	if httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := httpServer.Shutdown(ctx); err != nil {
			log.Printf("HTTP server shutdown error: %v", err)
		} else {
			log.Println("HTTP server shutdown successfully")
		}
	}

	// 这里可以添加其他资源的清理逻辑
	// 比如关闭数据库连接、停止监控等

	log.Println("Service shutdown completed")
	return nil
}

func RequestLogger(targetMux http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 请求一进入就记录
		log.Printf("%s %s %s from %s", time.Now().Format("2006-01-02 15:04:05"), r.Method, r.RequestURI, r.RemoteAddr)

		// 执行实际处理
		targetMux.ServeHTTP(w, r)
	})
}
