package main

import (
	"fmt"
	"log"
	"runtime"

	util "socks/relay/common"
)

func main() {
	fmt.Printf("Running on %s\n", runtime.GOOS)
	fmt.Println("Environment Variable Management Example")
	fmt.Println("=====================================")

	// 示例1: 设置和获取简单的环境变量
	fmt.Println("\n1. Setting and getting a simple environment variable:")
	key1 := "RELAY_APP_NAME"
	value1 := "BaseOS Relay Service"

	err := util.SetEnvironmentVariable(key1, value1)
	if err != nil {
		log.Printf("Error setting environment variable: %v", err)
	} else {
		fmt.Printf("✓ Set %s = %s\n", key1, value1)
	}

	retrievedValue1, err := util.GetEnvironmentVariable(key1)
	if err != nil {
		log.Printf("Error getting environment variable: %v", err)
	} else {
		fmt.Printf("✓ Retrieved %s = %s\n", key1, retrievedValue1)
	}

	// 示例2: 设置和获取包含特殊字符的环境变量
	fmt.Println("\n2. Setting and getting environment variable with special characters:")
	key2 := "RELAY_CONFIG_PATH"
	value2 := "C:\\Program Files\\BaseOS\\config with spaces\\relay.json"

	err = util.SetEnvironmentVariable(key2, value2)
	if err != nil {
		log.Printf("Error setting environment variable: %v", err)
	} else {
		fmt.Printf("✓ Set %s = %s\n", key2, value2)
	}

	retrievedValue2, err := util.GetEnvironmentVariable(key2)
	if err != nil {
		log.Printf("Error getting environment variable: %v", err)
	} else {
		fmt.Printf("✓ Retrieved %s = %s\n", key2, retrievedValue2)
	}

	// 示例3: 尝试获取不存在的环境变量
	fmt.Println("\n3. Trying to get a non-existent environment variable:")
	nonExistentKey := "NON_EXISTENT_VARIABLE"
	_, err = util.GetEnvironmentVariable(nonExistentKey)
	if err != nil {
		fmt.Printf("✓ Expected error for non-existent variable: %v\n", err)
	}

	// 示例4: 设置多个环境变量
	fmt.Println("\n4. Setting multiple environment variables:")
	envVars := map[string]string{
		"RELAY_SERVER_HOST": "localhost",
		"RELAY_SERVER_PORT": "8080",
		"RELAY_LOG_LEVEL":   "INFO",
		"RELAY_DEBUG_MODE":  "false",
	}

	for key, value := range envVars {
		err := util.SetEnvironmentVariable(key, value)
		if err != nil {
			log.Printf("Error setting %s: %v", key, err)
		} else {
			fmt.Printf("✓ Set %s = %s\n", key, value)
		}
	}

	// 验证所有变量
	fmt.Println("\n5. Verifying all set variables:")
	for key := range envVars {
		value, err := util.GetEnvironmentVariable(key)
		if err != nil {
			log.Printf("Error getting %s: %v", key, err)
		} else {
			fmt.Printf("✓ %s = %s\n", key, value)
		}
	}

	// 显示存储位置信息
	fmt.Println("\n6. Storage Information:")
	if runtime.GOOS == "windows" {
		fmt.Println("✓ Environment variables are stored in Windows system environment")
		fmt.Println("  You can view them in System Properties > Environment Variables")
	} else {
		fmt.Printf("✓ Environment variables are stored in: %s\n", util.LinuxEnvFilePath)
		fmt.Println("  You can view them by running: cat /BaseOS/.env")
	}

	fmt.Println("\nExample completed successfully!")
}
